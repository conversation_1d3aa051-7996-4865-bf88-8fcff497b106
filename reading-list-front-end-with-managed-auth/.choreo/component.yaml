schemaVersion: "1.2"
endpoints:
  - name: reading-list-frontend
    displayName: Reading List Frontend
    service:
      basePath: /
      port: 80
    type: REST
    networkVisibilities:
      - Public
dependencies:
  connectionReferences:
    - name: Reading List Connection
      resourceRef: service:/sample-project/reading-list-service/v1/d709e/PUBLIC
      environmentVariables:
        - name: SERVICEURL
        - name: CONSUMERKEY
        - name: CONSUMERSECRET
        - name: TOKENURL
        - name: CHOREOAPIKEY
