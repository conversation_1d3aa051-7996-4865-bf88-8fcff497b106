{"/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/postcss.config.cjs": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/postcss.config.cjs", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 18}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 12}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 21}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 4}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 82}, "end": {"line": 6, "column": 2}}, "locations": [{"start": {"line": 1, "column": 82}, "end": {"line": 6, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 82}, "end": {"line": 6, "column": 2}}, "loc": {"start": {"line": 1, "column": 82}, "end": {"line": 6, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/tailwind.config.cjs": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/tailwind.config.cjs", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 43}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 18}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 22}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 58}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 10}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 16}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 19}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 22}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 16}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 24}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 8}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 6}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 13}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 15}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 37}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 35}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 33}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 45}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 18}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 41}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 55}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 10}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 20}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 43}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 57}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 10}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 22}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 45}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 59}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 10}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 16}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 39}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 53}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 10}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 17}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 40}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 54}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 10}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 18}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 41}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 55}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 10}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 15}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 38}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 52}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 10}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 8}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 21}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 28}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 40}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 40}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 8}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 19}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 51}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 46}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 8}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 17}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 48}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 52}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 49}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 52}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 51}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 50}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 55}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 53}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 45}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 48}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 8}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 16}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 21}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 20}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 21}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 21}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 8}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 18}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 57}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 53}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 43}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 45}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 45}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 8}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 18}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 27}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 32}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 66}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 10}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 25}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 68}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 30}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 10}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 20}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 33}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 35}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 10}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 21}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 64}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 63}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 10}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 21}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 59}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 58}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 10}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 8}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 6}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 4}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 44}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 3387}, "end": {"line": 108, "column": 2}}, "locations": [{"start": {"line": 1, "column": 3387}, "end": {"line": 108, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 3387}, "end": {"line": 108, "column": 2}}, "loc": {"start": {"line": 1, "column": 3387}, "end": {"line": 108, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/public/config.js": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/public/config.js", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 18}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 64}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 86}, "end": {"line": 3, "column": 2}}, "locations": [{"start": {"line": 1, "column": 86}, "end": {"line": 3, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 86}, "end": {"line": 3, "column": 2}}, "loc": {"start": {"line": 1, "column": 86}, "end": {"line": 3, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/index.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/index.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 76}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 50}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 43}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 1}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 31}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 76}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 60}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 52}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 59}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 50}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 54}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 19}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 51}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 25}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 57}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 33}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 56}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 24}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 24}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 12}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 64}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 27}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 58}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 26}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 26}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 14}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 46}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 72}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 7}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 5}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 28}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 9}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 19}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 78}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 84}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 20}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 18}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 10}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 88}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 57}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 36}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 18}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 45}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 14}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 11}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 8}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 5}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 9}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 19}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 21}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 9}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 35}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 23}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 49}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 17}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 29}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 68}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 6}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 9}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 35}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 60}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 36}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 43}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 79}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 10}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 36}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 80}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 31}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 16}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 59}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 26}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 9}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 14}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 65}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 59}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 24}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 7}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 17}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 26}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 52}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 39}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 39}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 47}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 35}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 41}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 9}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 43}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 63}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 50}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 62}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 74}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 61}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 14}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 75}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 7}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 22}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 15}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 26}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 5}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 3}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 19}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 25}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 23}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 5}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 22}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 46}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 9}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 28}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 50}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 23}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 21}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 51}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 62}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 5}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 4}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 22}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 12}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 130}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 47}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 116}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 61}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 14}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 12}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 3}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 18}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 12}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 130}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 68}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 37}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 75}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 88}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 16}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 17}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 28}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 51}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 14}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 21}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 30}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 11}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 19}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 14}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 12}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 3}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 10}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 41}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 15}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 24}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 48}}, "213": {"start": {"line": 214, "column": 0}, "end": {"line": 214, "column": 34}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 35}}, "215": {"start": {"line": 216, "column": 0}, "end": {"line": 216, "column": 29}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 8}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 32}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 71}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 17}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 17}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 1}}}, "s": {"0": 0, "36": 0, "38": 0, "39": 0, "40": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "50": 0, "51": 0, "52": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "76": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "94": 0, "96": 0, "97": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "107": 0, "108": 0, "109": 0, "112": 0, "113": 0, "114": 0, "115": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "184": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "202": 0, "203": 0, "204": 0, "206": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 0, "213": 0, "214": 0, "215": 0, "216": 0, "217": 0, "218": 0, "219": 0, "220": 0, "222": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 223, "column": -1215}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 223, "column": -1215}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 223, "column": -1215}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 223, "column": -1215}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/main.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/main.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 26}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 40}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 48}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 63}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 52}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 20}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 19}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 21}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 47}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 75}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 20}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 19}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 21}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 15}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 23}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 33}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 26}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 33}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 29}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 22}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 21}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 26}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 19}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 22}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 25}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 10}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 22}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 20}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 21}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 2}}}, "s": {"0": 0, "16": 0, "17": 0, "18": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "26": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 49, "column": -374}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 49, "column": -374}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 49, "column": -374}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 49, "column": -374}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/retry.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/retry.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 46}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 14}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 41}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 6}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 26}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 15}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 64}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 14}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 41}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 26}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 6}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 4}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 7}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 44}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 53}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 54}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 57}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 20}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 19}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 43}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 93}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 68}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 88}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 18}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 75}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 100}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 18}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 12}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 90}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 18}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 5}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 3}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 2}}}, "s": {"0": 0, "18": 0, "19": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "32": 0, "33": 0, "34": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 2034}, "end": {"line": 55, "column": 2}}, "locations": [{"start": {"line": 1, "column": 2034}, "end": {"line": 55, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 2034}, "end": {"line": 55, "column": 2}}, "loc": {"start": {"line": 1, "column": 2034}, "end": {"line": 55, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/constants.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/constants.ts", "all": true, "statementMap": {"24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 76}}}, "s": {"24": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 826}, "end": {"line": 25, "column": 76}}, "locations": [{"start": {"line": 1, "column": 826}, "end": {"line": 25, "column": 76}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 826}, "end": {"line": 25, "column": 76}}, "loc": {"start": {"line": 1, "column": 826}, "end": {"line": 25, "column": 76}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/delete-books.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/delete-books.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 47}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 19}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 21}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 4}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 83}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 18}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 1}}}, "s": {"0": 0, "20": 0, "21": 0, "22": 0, "23": 0, "25": 0, "26": 0, "27": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 953}, "end": {"line": 28, "column": 1}}, "locations": [{"start": {"line": 1, "column": 953}, "end": {"line": 28, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 953}, "end": {"line": 28, "column": 1}}, "loc": {"start": {"line": 1, "column": 953}, "end": {"line": 28, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/get-books.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/get-books.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 34}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 19}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 18}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 4}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 77}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 43}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 1}}}, "s": {"0": 0, "23": 0, "24": 0, "25": 0, "26": 0, "28": 0, "29": 0, "30": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1033}, "end": {"line": 31, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1033}, "end": {"line": 31, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1033}, "end": {"line": 31, "column": 1}}, "loc": {"start": {"line": 1, "column": 1033}, "end": {"line": 31, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/post-books.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/post-books.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 49}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 19}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 19}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 18}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 4}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 77}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 18}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 1}}}, "s": {"0": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "27": 0, "28": 0, "29": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1003}, "end": {"line": 30, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1003}, "end": {"line": 30, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1003}, "end": {"line": 30, "column": 1}}, "loc": {"start": {"line": 1, "column": 1003}, "end": {"line": 30, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/types/book.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/api/books/types/book.ts", "all": true, "statementMap": {"23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 20}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 22}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 16}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 22}}}, "s": {"23": 0, "24": 0, "25": 0, "26": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 844}, "end": {"line": 28, "column": 1}}, "locations": [{"start": {"line": 1, "column": 844}, "end": {"line": 28, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 844}, "end": {"line": 28, "column": 1}}, "loc": {"start": {"line": 1, "column": 844}, "end": {"line": 28, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/book/book-card.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/book/book-card.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 39}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 24}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 23}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 18}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 18}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 11}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 73}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 66}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 35}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 30}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 4}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 37}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 20}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 26}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 5}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 31}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 4}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 10}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 15}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 12}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 37}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 36}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 35}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 36}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 28}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 23}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 20}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 58}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 115}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 37}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 37}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 63}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 20}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 99}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 35}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 49}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 22}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 21}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 31}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 27}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 43}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 37}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 30}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 81}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 66}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 18}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 51}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 46}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 65}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 68}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 23}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 18}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 17}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 95}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 28}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 19}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 18}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 79}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 56}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 69}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 18}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 16}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 22}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 47}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 98}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 57}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 46}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 41}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 18}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 66}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 16}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 21}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 13}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 25}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 33}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 50}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 39}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 27}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 102}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 28}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 29}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 30}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 8}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 17}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 1}}}, "s": {"0": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "23": 1, "24": 7, "26": 7, "27": 2, "28": 2, "30": 7, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "37": 7, "38": 7, "39": 7, "40": 7, "41": 7, "42": 7, "43": 7, "44": 7, "45": 7, "46": 7, "47": 7, "49": 7, "50": 7, "51": 7, "53": 7, "54": 7, "55": 7, "56": 7, "58": 7, "59": 7, "60": 7, "61": 7, "62": 7, "63": 7, "64": 7, "65": 7, "66": 7, "67": 7, "68": 7, "69": 7, "70": 7, "72": 7, "73": 7, "74": 7, "75": 7, "78": 7, "79": 7, "80": 7, "81": 7, "82": 7, "85": 7, "86": 7, "87": 7, "88": 7, "89": 7, "90": 7, "92": 7, "93": 7, "94": 7, "95": 7, "96": 7, "97": 7, "98": 7, "99": 7, "100": 7, "101": 7, "103": 7, "104": 7, "105": 7, "106": 7, "107": 7, "108": 7, "109": 7, "110": 7, "111": 7, "112": 7, "113": 7, "115": 7}, "branchMap": {"0": {"type": "branch", "line": 24, "loc": {"start": {"line": 24, "column": 7}, "end": {"line": 116, "column": 1}}, "locations": [{"start": {"line": 24, "column": 7}, "end": {"line": 116, "column": 1}}]}, "1": {"type": "branch", "line": 56, "loc": {"start": {"line": 56, "column": 82}, "end": {"line": 56, "column": 99}}, "locations": [{"start": {"line": 56, "column": 82}, "end": {"line": 56, "column": 99}}]}, "2": {"type": "branch", "line": 59, "loc": {"start": {"line": 59, "column": 35}, "end": {"line": 59, "column": 47}}, "locations": [{"start": {"line": 59, "column": 35}, "end": {"line": 59, "column": 47}}]}, "3": {"type": "branch", "line": 27, "loc": {"start": {"line": 27, "column": 28}, "end": {"line": 29, "column": 4}}, "locations": [{"start": {"line": 27, "column": 28}, "end": {"line": 29, "column": 4}}]}, "4": {"type": "branch", "line": 31, "loc": {"start": {"line": 31, "column": 30}, "end": {"line": 36, "column": 4}}, "locations": [{"start": {"line": 31, "column": 30}, "end": {"line": 36, "column": 4}}]}}, "b": {"0": [7], "1": [0], "2": [0], "3": [2], "4": [1]}, "fnMap": {"0": {"name": "BookCard", "decl": {"start": {"line": 24, "column": 7}, "end": {"line": 116, "column": 1}}, "loc": {"start": {"line": 24, "column": 7}, "end": {"line": 116, "column": 1}}, "line": 24}, "1": {"name": "handleDeleteClick", "decl": {"start": {"line": 27, "column": 28}, "end": {"line": 29, "column": 4}}, "loc": {"start": {"line": 27, "column": 28}, "end": {"line": 29, "column": 4}}, "line": 27}, "2": {"name": "handleConfirmDelete", "decl": {"start": {"line": 31, "column": 30}, "end": {"line": 36, "column": 4}}, "loc": {"start": {"line": 31, "column": 30}, "end": {"line": 36, "column": 4}}, "line": 31}, "3": {"name": "onClose", "decl": {"start": {"line": 106, "column": 17}, "end": {"line": 106, "column": 50}}, "loc": {"start": {"line": 106, "column": 17}, "end": {"line": 106, "column": 50}}, "line": 106}}, "f": {"0": 7, "1": 2, "2": 1, "3": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/book/book-list.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/book/book-list.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 23}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 39}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 41}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 51}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 40}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 2}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 99}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 53}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 62}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 48}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 34}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 66}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 6}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 4}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 39}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 26}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 30}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 9}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 61}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 38}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 37}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 90}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 48}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 39}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 9}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 9}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 37}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 75}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 7}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 24}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 48}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 44}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 56}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 58}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 65}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 11}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 7}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 31}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 25}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 23}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 64}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 24}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 66}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 24}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 66}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 18}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 21}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 9}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 9}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 21}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 54}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 16}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 5}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 20}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 51}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 68}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 10}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 12}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 33}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 92}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 15}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 127}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 17}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 54}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 83}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 16}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 16}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 55}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 19}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 31}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 25}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 33}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 34}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 35}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 82}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 54}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 21}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 80}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 42}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 35}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 21}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 16}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 14}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 57}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 43}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 115}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 18}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 62}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 61}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 31}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 14}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 16}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 38}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 19}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 36}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 42}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 37}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 30}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 14}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 19}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 28}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 34}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 24}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 59}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 61}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 61}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 16}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 30}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 14}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 16}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 14}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 39}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 24}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 82}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 56}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 82}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 17}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 18}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 42}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 29}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 78}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 32}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 31}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 44}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 47}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 68}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 32}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 19}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 32}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 31}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 89}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 101}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 68}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 20}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 76}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 65}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 56}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 91}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 82}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 18}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 60}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 84}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 46}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 50}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 25}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 18}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 14}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 12}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 13}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 1}}}, "s": {"0": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "30": 0, "31": 0, "32": 0, "33": 0, "36": 0, "37": 0, "38": 0, "39": 0, "42": 0, "43": 0, "45": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "58": 0, "59": 0, "60": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "90": 0, "91": 0, "93": 0, "95": 0, "96": 0, "97": 0, "99": 0, "100": 0, "101": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "117": 0, "118": 0, "119": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "198": 0, "200": 0, "201": 0, "202": 0, "204": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 205, "column": -3104}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 205, "column": -3104}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 205, "column": -3104}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 205, "column": -3104}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/layout/header.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/layout/header.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 79}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 30}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 42}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 86}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 4}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 10}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 133}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 77}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 53}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 55}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 57}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 122}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 48}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 19}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 16}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 14}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 63}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 32}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 20}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 57}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 39}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 76}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 81}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 20}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 21}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 33}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 25}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 38}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 55}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 46}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 35}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 23}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 18}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 14}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 63}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 32}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 89}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 89}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 56}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 19}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 14}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 12}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 28}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 75}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 57}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 22}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 16}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 55}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 78}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 83}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 22}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 23}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 35}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 40}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 79}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 48}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 37}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 25}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 17}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 16}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 14}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 13}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 1}}}, "s": {"0": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "26": 0, "27": 0, "28": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "54": 0, "55": 0, "56": 0, "57": 0, "59": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "86": 0, "87": 0, "88": 0, "89": 0, "91": 0, "92": 0, "94": 0, "96": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 97, "column": -1805}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 97, "column": -1805}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 97, "column": -1805}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 97, "column": -1805}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/layout/main-layout.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/layout/main-layout.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 76}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 66}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 34}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 43}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 4}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 10}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 95}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 96}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 69}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 59}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 13}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 79}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 99}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 103}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 12}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 10}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 1}}}, "s": {"0": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "25": 0, "26": 0, "27": 0, "29": 0, "30": 0, "31": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "40": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 41, "column": -487}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 41, "column": -487}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 41, "column": -487}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 41, "column": -487}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/modal/modal.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/modal/modal.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 50}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 81}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 10}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 51}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 82}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 25}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 23}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 39}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 31}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 31}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 38}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 33}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 29}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 66}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 27}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 55}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 87}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 29}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 27}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 43}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 44}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 45}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 42}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 47}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 42}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 155}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 94}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 25}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 31}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 26}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 39}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 25}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 33}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 31}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 32}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 37}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 147}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 27}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 37}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 263}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 27}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 50}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 41}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 19}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 27}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 22}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 29}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 31}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 16}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 14}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 15}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 17}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 1}}}, "s": {"0": 0, "30": 0, "31": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "45": 0, "46": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "77": 0, "78": 0, "79": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "90": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 91, "column": -642}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 91, "column": -642}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 91, "column": -642}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 91, "column": -642}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/modal/fragments/add-item.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/modal/fragments/add-item.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 23}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 41}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 51}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 40}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 2}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 54}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 38}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 41}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 43}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 50}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 58}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 80}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 30}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 62}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 24}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 44}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 5}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 25}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 46}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 5}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 25}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 47}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 4}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 38}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 32}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 26}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 9}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 29}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 28}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 30}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 15}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 8}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 31}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 48}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 19}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 20}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 27}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 20}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 23}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 21}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 49}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 59}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 15}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 29}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 5}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 4}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 29}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 17}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 18}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 25}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 18}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 21}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 4}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 10}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 10}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 21}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 27}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 26}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 55}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 14}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 40}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 82}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 19}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 17}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 36}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 70}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 34}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 11}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 19}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 14}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 33}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 14}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 28}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 23}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 50}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 44}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 30}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 10}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 14}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 24}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 24}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 51}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 47}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 31}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 10}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 15}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 32}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 24}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 30}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 33}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 10}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 12}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 12}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 1}}}, "s": {"0": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "45": 0, "46": 0, "48": 0, "49": 0, "50": 0, "52": 0, "53": 0, "54": 0, "56": 0, "57": 0, "58": 0, "60": 0, "61": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "71": 0, "72": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "112": 0, "113": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "142": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 143, "column": -1028}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 143, "column": -1028}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 143, "column": -1028}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 143, "column": -1028}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/badge.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/badge.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 66}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 26}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 171}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 3}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 15}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 16}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 93}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 18}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 92}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 20}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 98}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 35}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 84}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 86}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 79}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 8}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 6}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 22}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 25}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 6}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 3}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 2}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 62}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 82}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 1}}}, "s": {"0": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "31": 7, "32": 7, "33": 7}, "branchMap": {"0": {"type": "branch", "line": 32, "loc": {"start": {"line": 32, "column": 0}, "end": {"line": 34, "column": 1}}, "locations": [{"start": {"line": 32, "column": 0}, "end": {"line": 34, "column": 1}}]}}, "b": {"0": [7]}, "fnMap": {"0": {"name": "Badge", "decl": {"start": {"line": 32, "column": 0}, "end": {"line": 34, "column": 1}}, "loc": {"start": {"line": 32, "column": 0}, "end": {"line": 34, "column": 1}}, "line": 32}}, "f": {"0": 7}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/button.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/button.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 27}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 260}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 3}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 15}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 16}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 74}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 90}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 98}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 82}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 62}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 64}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 17}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 122}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 8}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 13}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 34}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 34}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 35}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 26}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 8}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 6}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 22}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 25}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 22}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 6}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 3}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 2}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 64}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 3}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 97}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 8}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 43}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 12}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 11}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 68}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 17}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 38}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 18}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 21}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 14}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 49}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 46}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 23}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 31}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 19}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 36}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 21}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 21}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 20}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 35}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 29}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 14}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 17}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 36}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 33}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 129}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 14}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 16}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 18}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 13}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 3}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 2}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 30}}}, "s": {"0": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "41": 1, "42": 1, "43": 22, "44": 22, "45": 22, "46": 22, "47": 22, "48": 22, "49": 22, "50": 22, "51": 22, "52": 22, "54": 22, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 1, "68": 1, "69": 1, "70": 1, "71": 1, "72": 1, "73": 1, "74": 1, "76": 22, "77": 22, "79": 22, "80": 1, "81": 1}, "branchMap": {"0": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 2}, "end": {"line": 80, "column": 3}}, "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 80, "column": 3}}]}, "1": {"type": "branch", "line": 47, "loc": {"start": {"line": 47, "column": 17}, "end": {"line": 47, "column": 34}}, "locations": [{"start": {"line": 47, "column": 17}, "end": {"line": 47, "column": 34}}]}, "2": {"type": "branch", "line": 52, "loc": {"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 38}}, "locations": [{"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 38}}]}, "3": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 9}, "end": {"line": 75, "column": 16}}, "locations": [{"start": {"line": 55, "column": 9}, "end": {"line": 75, "column": 16}}]}}, "b": {"0": [22], "1": [0], "2": [21], "3": [1]}, "fnMap": {}, "f": {}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/card.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/card.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 31}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 84}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 37}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 8}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 15}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 20}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 101}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 17}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 8}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 16}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 6}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 2}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 26}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 90}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 37}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 91}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 2}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 38}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 99}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 47}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 7}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 15}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 85}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 16}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 16}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 9}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 2}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 36}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 41}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 37}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 87}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 3}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 48}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 91}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 37}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 70}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 2}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 40}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 90}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 37}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 88}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 2}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 38}}}, "s": {"0": 1, "4": 1, "5": 1, "6": 7, "7": 7, "8": 7, "9": 7, "10": 7, "11": 7, "12": 7, "13": 7, "15": 1, "16": 1, "18": 1, "19": 1, "20": 0, "22": 1, "23": 1, "25": 1, "26": 1, "27": 0, "28": 0, "29": 0, "30": 0, "32": 0, "33": 0, "35": 1, "36": 1, "38": 1, "41": 1, "42": 0, "43": 1, "44": 1, "46": 1, "47": 1, "48": 7, "50": 1, "51": 1, "53": 1, "54": 1, "55": 7, "57": 1, "58": 1}, "branchMap": {"0": {"type": "branch", "line": 6, "loc": {"start": {"line": 6, "column": 2}, "end": {"line": 14, "column": 6}}, "locations": [{"start": {"line": 6, "column": 2}, "end": {"line": 14, "column": 6}}]}, "1": {"type": "branch", "line": 48, "loc": {"start": {"line": 48, "column": 2}, "end": {"line": 49, "column": 70}}, "locations": [{"start": {"line": 48, "column": 2}, "end": {"line": 49, "column": 70}}]}, "2": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 2}, "end": {"line": 56, "column": 88}}, "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 56, "column": 88}}]}}, "b": {"0": [7], "1": [7], "2": [7]}, "fnMap": {}, "f": {}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/confirmation-dialog.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/confirmation-dialog.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 36}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 9}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 10}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 12}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 8}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 14}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 26}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 24}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 22}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 20}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 29}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 10}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 10}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 21}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 23}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 19}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 31}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 15}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 14}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 40}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 75}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 24}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 19}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 17}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 75}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 31}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 32}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 31}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 25}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 19}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 14}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 51}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 39}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 41}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 66}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 16}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 74}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 12}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 12}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 1}}}, "s": {"0": 1, "18": 1, "19": 7, "20": 7, "21": 7, "22": 7, "23": 7, "24": 7, "25": 7, "26": 7, "27": 7, "28": 7, "29": 7, "30": 7, "31": 7, "32": 7, "33": 7, "34": 7, "35": 7, "36": 7, "37": 7, "38": 7, "39": 7, "40": 7, "41": 7, "42": 7, "43": 7, "44": 7, "45": 7, "47": 7, "48": 7, "49": 7, "52": 7, "53": 7, "54": 7, "55": 7, "56": 7, "58": 7, "59": 7, "60": 7, "62": 7}, "branchMap": {"0": {"type": "branch", "line": 19, "loc": {"start": {"line": 19, "column": 7}, "end": {"line": 63, "column": 1}}, "locations": [{"start": {"line": 19, "column": 7}, "end": {"line": 63, "column": 1}}]}, "1": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 49}, "end": {"line": 43, "column": 75}}, "locations": [{"start": {"line": 43, "column": 49}, "end": {"line": 43, "column": 75}}]}}, "b": {"0": [7], "1": [0]}, "fnMap": {"0": {"name": "ConfirmationDialog", "decl": {"start": {"line": 19, "column": 7}, "end": {"line": 63, "column": 1}}, "loc": {"start": {"line": 19, "column": 7}, "end": {"line": 63, "column": 1}}, "line": 19}}, "f": {"0": 7}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/error-boundary.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/error-boundary.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 56}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 60}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 25}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 20}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 4}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 63}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 37}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 3}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 64}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 65}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 3}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 31}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 57}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 4}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 19}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 30}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 32}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 35}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 7}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 14}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 76}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 44}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 48}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 118}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 70}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 20}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 77}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 25}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 59}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 59}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 18}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 78}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 47}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 84}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 28}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 83}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 46}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 44}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 24}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 26}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 57}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 23}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 35}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 58}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 53}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 51}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 25}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 69}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 20}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 26}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 17}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 14}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 5}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 31}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 3}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 1}}}, "s": {"0": 0, "16": 0, "17": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "25": 0, "26": 0, "27": 0, "29": 0, "30": 0, "31": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "52": 0, "54": 0, "55": 0, "56": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "72": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "81": 0, "83": 0, "84": 0, "85": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 86, "column": -1729}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 86, "column": -1729}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 86, "column": -1729}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 86, "column": -1729}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/fade-in.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/fade-in.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 56}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 27}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 28}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 31}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 30}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 32}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 2}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 24}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 11}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 12}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 17}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 19}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 10}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 17}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 10}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 15}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 44}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 42}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 19}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 17}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 14}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 33}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 8}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 16}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 16}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 17}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 1}}}, "s": {"0": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "35": 0, "36": 0, "38": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 39, "column": -258}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 39, "column": -258}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 39, "column": -258}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 39, "column": -258}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/index.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 50}}}, "s": {"0": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 726}, "end": {"line": 14, "column": 49}}, "locations": [{"start": {"line": 1, "column": 726}, "end": {"line": 14, "column": 49}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 726}, "end": {"line": 14, "column": 49}}, "loc": {"start": {"line": 1, "column": 726}, "end": {"line": 14, "column": 49}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/input.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/input.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 31}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 61}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 61}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 77}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 12}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 33}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 19}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 16}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 29}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 114}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 19}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 18}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 14}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 21}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 22}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 24}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 363}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 73}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 21}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 12}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 19}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 20}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 10}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 19}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 63}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 19}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 14}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 12}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 3}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 2}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 28}}}, "s": {"0": 0, "9": 0, "10": 0, "11": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "39": 0, "41": 0, "42": 0, "43": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 46, "column": -349}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 46, "column": -349}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 46, "column": -349}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 46, "column": -349}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/lazy-wrapper.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/lazy-wrapper.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 82}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 27}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 75}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 34}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 10}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 80}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 1}}}, "s": {"0": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "19": 0, "20": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 21, "column": -204}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 21, "column": -204}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 21, "column": -204}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 21, "column": -204}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/loading-spinner.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/loading-spinner.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 21}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 16}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 16}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 16}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 2}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 81}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 10}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 15}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 20}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 68}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 26}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 17}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 8}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 31}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 19}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 20}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 25}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 23}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 8}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 6}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 1}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 68}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 10}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 53}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 27}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 19}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 17}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 53}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 20}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 31}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 35}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 12}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 23}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 24}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 29}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 27}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 12}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 10}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 9}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 10}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 1}}}, "s": {"0": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "32": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "54": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1203}, "end": {"line": 55, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1203}, "end": {"line": 55, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1203}, "end": {"line": 55, "column": 1}}, "loc": {"start": {"line": 1, "column": 1203}, "end": {"line": 55, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/modal.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/modal.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 55}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 21}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 17}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 17}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 18}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 18}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 2}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 23}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 9}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 10}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 8}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 14}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 11}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 9}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 14}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 25}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 16}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 79}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 10}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 57}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 67}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 25}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 29}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 39}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 31}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 31}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 38}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 33}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 29}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 72}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 27}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 55}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 87}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 29}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 33}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 43}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 44}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 45}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 42}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 47}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 42}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 27}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 34}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 30}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 130}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 35}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 18}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 67}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 29}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 33}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 29}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 81}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 29}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 35}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 39}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 27}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 37}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 33}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 39}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 52}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 47}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 60}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 29}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 22}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 33}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 85}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 33}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 39}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 54}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 91}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 29}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 31}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 16}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 14}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 15}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 17}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 1}}}, "s": {"0": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "27": 1, "28": 7, "29": 7, "30": 7, "31": 7, "32": 7, "33": 7, "34": 7, "35": 7, "36": 7, "37": 7, "38": 7, "39": 7, "40": 7, "41": 7, "42": 7, "43": 7, "44": 7, "45": 7, "46": 7, "47": 7, "48": 7, "50": 7, "51": 7, "53": 7, "54": 7, "55": 7, "56": 7, "57": 7, "58": 7, "59": 7, "60": 7, "61": 7, "62": 7, "64": 7, "65": 7, "66": 7, "67": 7, "68": 7, "69": 7, "71": 7, "72": 7, "73": 7, "74": 7, "75": 7, "77": 7, "78": 7, "80": 7, "81": 7, "82": 7, "83": 7, "84": 7, "85": 7, "87": 7, "88": 7, "89": 7, "91": 7, "93": 7, "94": 7, "95": 7, "96": 7, "99": 7, "101": 7, "102": 7, "103": 7, "104": 7, "105": 7, "106": 7, "107": 7, "109": 7}, "branchMap": {"0": {"type": "branch", "line": 28, "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 110, "column": 1}}, "locations": [{"start": {"line": 28, "column": 7}, "end": {"line": 110, "column": 1}}]}}, "b": {"0": [7]}, "fnMap": {"0": {"name": "Modal", "decl": {"start": {"line": 28, "column": 7}, "end": {"line": 110, "column": 1}}, "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 110, "column": 1}}, "line": 28}}, "f": {"0": 7}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/select.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/select.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 56}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 24}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 8}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 11}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 10}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 35}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 8}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 8}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 19}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 12}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 17}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 72}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 10}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 48}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 17}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 118}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 17}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 16}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 69}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 34}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 25}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 26}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 292}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 74}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 14}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 45}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 67}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 19}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 100}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 90}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 19}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 27}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 21}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 31}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 51}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 35}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 31}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 200}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 38}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 31}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 36}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 44}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 23}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 76}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 94}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 72}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 21}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 38}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 44}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 38}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 22}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 27}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 98}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 38}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 29}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 35}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 114}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 74}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 31}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 31}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 23}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 33}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 17}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 30}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 23}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 14}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 16}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 17}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 61}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 17}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 12}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 10}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 1}}}, "s": {"0": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "74": 0, "75": 0, "77": 0, "78": 0, "79": 0, "80": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "102": 0, "104": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 105, "column": -1824}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 105, "column": -1824}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 105, "column": -1824}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 105, "column": -1824}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/skeleton.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/skeleton.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 82}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 91}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 1}}}, "s": {"0": 0, "4": 0, "5": 0, "6": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 9, "column": -96}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 9, "column": -96}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 9, "column": -96}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 9, "column": -96}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/stagger-container.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/stagger-container.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 56}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 34}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 11}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 21}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 19}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 10}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 27}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 10}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 15}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 22}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 23}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 17}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 31}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 18}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 21}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 23}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 40}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 42}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 12}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 10}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 8}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 16}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 16}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 17}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 1}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 29}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 11}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 10}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 60}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 10}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 15}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 17}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 38}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 18}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 15}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 21}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 23}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 26}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 37}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 12}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 10}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 8}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 16}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 16}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 17}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 1}}}, "s": {"0": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "31": 0, "32": 0, "34": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "55": 0, "56": 0, "58": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 59, "column": -388}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 59, "column": -388}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 59, "column": -388}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 59, "column": -388}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/theme-toggle.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/components/ui/theme-toggle.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 31}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 41}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 18}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 50}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 49}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 56}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 13}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 10}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 73}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 53}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 15}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 21}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 41}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 24}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 103}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 56}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 82}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 97}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 12}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 44}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 38}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 50}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 17}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 9}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 10}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 1}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 38}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 54}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 30}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 29}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 58}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 12}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 52}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 5}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 4}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 10}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 11}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 28}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 20}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 93}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 55}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 77}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 8}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 76}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 91}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 51}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 13}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 1}}}, "s": {"0": 0, "6": 0, "7": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "35": 0, "37": 0, "38": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "58": 0, "59": 0, "60": 0, "62": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 63, "column": -936}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 63, "column": -936}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 63, "column": -936}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 63, "column": -936}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/theme-context.tsx": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/theme-context.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 78}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 76}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 76}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 51}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 40}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 41}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 5}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 74}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 29}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 30}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 5}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 37}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 5}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 19}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 31}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 43}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 31}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 42}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 14}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 51}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 7}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 37}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 44}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 45}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 41}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 34}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 6}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 18}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 29}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 75}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 34}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 33}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 24}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 9}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 8}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 58}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 74}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 5}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 14}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 17}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 10}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 13}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 16}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 4}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 81}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 1}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 28}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 43}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 30}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 68}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 3}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 17}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 1}}}, "s": {"0": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "27": 0, "28": 0, "29": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "37": 0, "40": 0, "41": 0, "42": 0, "45": 0, "46": 0, "48": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "59": 0, "60": 0, "61": 0, "62": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "70": 0, "71": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 80, "column": -270}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 80, "column": -270}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 80, "column": -270}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 80, "column": -270}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/utils.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/utils.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 45}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 45}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 31}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 1}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 57}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 27}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 40}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 20}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 18}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 19}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 5}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 1}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 60}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 10}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 14}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 37}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 30}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 38}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 26}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 52}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 4}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 1}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 38}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 99}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 1}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 49}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 52}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 1}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 54}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 74}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 1}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 54}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 50}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 32}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 1}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 71}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 44}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 46}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 1}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 52}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 38}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 88}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 3}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 17}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 1}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 24}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 40}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 9}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 39}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 13}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 18}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 5}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 4}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 46}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 9}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 39}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 13}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 5}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 4}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 34}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 9}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 35}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 13}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 5}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 4}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 2}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 50}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 57}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 1}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 71}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 7}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 46}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 16}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 11}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 9}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 58}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 28}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 42}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 24}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 35}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 42}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 18}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 13}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 19}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 5}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 3}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 1}}}, "s": {"0": 1, "7": 1, "8": 67, "9": 67, "14": 1, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "27": 1, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "41": 1, "42": 0, "43": 0, "48": 1, "49": 3, "50": 3, "55": 1, "56": 10, "57": 10, "62": 1, "63": 4, "64": 4, "65": 4, "70": 1, "71": 2, "72": 1, "73": 1, "78": 1, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "88": 1, "89": 1, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 1, "97": 0, "98": 0, "99": 0, "101": 0, "102": 0, "103": 1, "104": 0, "105": 0, "106": 0, "108": 0, "109": 0, "110": 1, "115": 1, "116": 0, "117": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0}, "branchMap": {"0": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "locations": [{"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}]}, "1": {"type": "branch", "line": 49, "loc": {"start": {"line": 49, "column": 7}, "end": {"line": 51, "column": 1}}, "locations": [{"start": {"line": 49, "column": 7}, "end": {"line": 51, "column": 1}}]}, "2": {"type": "branch", "line": 56, "loc": {"start": {"line": 56, "column": 7}, "end": {"line": 58, "column": 1}}, "locations": [{"start": {"line": 56, "column": 7}, "end": {"line": 58, "column": 1}}]}, "3": {"type": "branch", "line": 57, "loc": {"start": {"line": 57, "column": 52}, "end": {"line": 57, "column": 72}}, "locations": [{"start": {"line": 57, "column": 52}, "end": {"line": 57, "column": 72}}]}, "4": {"type": "branch", "line": 63, "loc": {"start": {"line": 63, "column": 7}, "end": {"line": 66, "column": 1}}, "locations": [{"start": {"line": 63, "column": 7}, "end": {"line": 66, "column": 1}}]}, "5": {"type": "branch", "line": 71, "loc": {"start": {"line": 71, "column": 7}, "end": {"line": 74, "column": 1}}, "locations": [{"start": {"line": 71, "column": 7}, "end": {"line": 74, "column": 1}}]}, "6": {"type": "branch", "line": 72, "loc": {"start": {"line": 72, "column": 32}, "end": {"line": 74, "column": 1}}, "locations": [{"start": {"line": 72, "column": 32}, "end": {"line": 74, "column": 1}}]}}, "b": {"0": [67], "1": [3], "2": [10], "3": [18], "4": [4], "5": [2], "6": [1]}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "line": 8}, "1": {"name": "formatDate", "decl": {"start": {"line": 15, "column": 7}, "end": {"line": 22, "column": 1}}, "loc": {"start": {"line": 15, "column": 7}, "end": {"line": 22, "column": 1}}, "line": 15}, "2": {"name": "debounce", "decl": {"start": {"line": 28, "column": 7}, "end": {"line": 37, "column": 1}}, "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 37, "column": 1}}, "line": 28}, "3": {"name": "generateId", "decl": {"start": {"line": 42, "column": 7}, "end": {"line": 44, "column": 1}}, "loc": {"start": {"line": 42, "column": 7}, "end": {"line": 44, "column": 1}}, "line": 42}, "4": {"name": "capitalize", "decl": {"start": {"line": 49, "column": 7}, "end": {"line": 51, "column": 1}}, "loc": {"start": {"line": 49, "column": 7}, "end": {"line": 51, "column": 1}}, "line": 49}, "5": {"name": "formatStatus", "decl": {"start": {"line": 56, "column": 7}, "end": {"line": 58, "column": 1}}, "loc": {"start": {"line": 56, "column": 7}, "end": {"line": 58, "column": 1}}, "line": 56}, "6": {"name": "isValidEmail", "decl": {"start": {"line": 63, "column": 7}, "end": {"line": 66, "column": 1}}, "loc": {"start": {"line": 63, "column": 7}, "end": {"line": 66, "column": 1}}, "line": 63}, "7": {"name": "truncateText", "decl": {"start": {"line": 71, "column": 7}, "end": {"line": 74, "column": 1}}, "loc": {"start": {"line": 71, "column": 7}, "end": {"line": 74, "column": 1}}, "line": 71}, "8": {"name": "getSystemTheme", "decl": {"start": {"line": 79, "column": 7}, "end": {"line": 84, "column": 1}}, "loc": {"start": {"line": 79, "column": 7}, "end": {"line": 84, "column": 1}}, "line": 79}, "9": {"name": "get", "decl": {"start": {"line": 90, "column": 7}, "end": {"line": 96, "column": 4}}, "loc": {"start": {"line": 90, "column": 7}, "end": {"line": 96, "column": 4}}, "line": 90}, "10": {"name": "set", "decl": {"start": {"line": 97, "column": 7}, "end": {"line": 103, "column": 4}}, "loc": {"start": {"line": 97, "column": 7}, "end": {"line": 103, "column": 4}}, "line": 97}, "11": {"name": "remove", "decl": {"start": {"line": 104, "column": 10}, "end": {"line": 110, "column": 4}}, "loc": {"start": {"line": 104, "column": 10}, "end": {"line": 110, "column": 4}}, "line": 104}, "12": {"name": "sleep", "decl": {"start": {"line": 116, "column": 7}, "end": {"line": 118, "column": 1}}, "loc": {"start": {"line": 116, "column": 7}, "end": {"line": 118, "column": 1}}, "line": 116}, "13": {"name": "copyToClipboard", "decl": {"start": {"line": 123, "column": 0}, "end": {"line": 141, "column": 1}}, "loc": {"start": {"line": 123, "column": 0}, "end": {"line": 141, "column": 1}}, "line": 123}}, "f": {"0": 67, "1": 0, "2": 0, "3": 0, "4": 3, "5": 10, "6": 4, "7": 2, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/index.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}}, "s": {"0": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 180}, "end": {"line": 3, "column": 64}}, "locations": [{"start": {"line": 1, "column": 180}, "end": {"line": 3, "column": 64}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 180}, "end": {"line": 3, "column": 64}}, "loc": {"start": {"line": 1, "column": 180}, "end": {"line": 3, "column": 64}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/use-debounced-callback.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/use-debounced-callback.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 80}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 14}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 15}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 6}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 46}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 21}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 34}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 31}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 41}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 7}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 26}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 16}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 12}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 21}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 4}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}}, "s": {"0": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 558}, "end": {"line": 24, "column": 1}}, "locations": [{"start": {"line": 1, "column": 558}, "end": {"line": 24, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 558}, "end": {"line": 24, "column": 1}}, "loc": {"start": {"line": 1, "column": 558}, "end": {"line": 24, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/use-focus-trap.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/use-focus-trap.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 56}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 49}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 19}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 51}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 43}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 57}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 80}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 6}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 61}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 87}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 48}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 34}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 23}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 54}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 31}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 29}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 9}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 14}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 53}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 32}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 29}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 9}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 7}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 6}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 51}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 31}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 28}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 7}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 6}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 56}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 59}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 26}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 18}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 61}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 64}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 6}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 17}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 22}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 1}}}, "s": {"0": 1, "6": 1, "7": 7, "9": 7, "10": 7, "12": 0, "13": 0, "14": 0, "15": 0, "17": 0, "18": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "36": 0, "37": 0, "39": 0, "40": 0, "41": 0, "43": 0, "44": 0, "47": 7, "49": 7, "50": 0, "51": 0, "52": 0, "53": 7, "55": 7, "56": 7}, "branchMap": {"0": {"type": "branch", "line": 7, "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 57, "column": 1}}, "locations": [{"start": {"line": 7, "column": 7}, "end": {"line": 57, "column": 1}}]}, "1": {"type": "branch", "line": 10, "loc": {"start": {"line": 10, "column": 12}, "end": {"line": 54, "column": 5}}, "locations": [{"start": {"line": 10, "column": 12}, "end": {"line": 54, "column": 5}}]}, "2": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 44}}, "locations": [{"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 44}}]}, "3": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 44}, "end": {"line": 48, "column": 24}}, "locations": [{"start": {"line": 11, "column": 44}, "end": {"line": 48, "column": 24}}]}}, "b": {"0": [7], "1": [7], "2": [2], "3": [0]}, "fnMap": {"0": {"name": "useFocusTrap", "decl": {"start": {"line": 7, "column": 7}, "end": {"line": 57, "column": 1}}, "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 57, "column": 1}}, "line": 7}, "1": {"name": "handleTabKey", "decl": {"start": {"line": 21, "column": 25}, "end": {"line": 35, "column": 6}}, "loc": {"start": {"line": 21, "column": 25}, "end": {"line": 35, "column": 6}}, "line": 21}, "2": {"name": "handleEscapeKey", "decl": {"start": {"line": 37, "column": 28}, "end": {"line": 42, "column": 6}}, "loc": {"start": {"line": 37, "column": 28}, "end": {"line": 42, "column": 6}}, "line": 37}}, "f": {"0": 7, "1": 0, "2": 0}}, "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/use-keyboard-navigation.ts": {"path": "/home/<USER>/Music/NextNextTry/choreo-sample-book-list-app/reading-list-front-end-with-managed-auth/src/lib/hooks/use-keyboard-navigation.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 47}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 39}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 10}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 11}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 12}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 14}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 14}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 15}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 17}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 31}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 36}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 31}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 27}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 26}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 21}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 22}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 16}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 22}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 23}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 16}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 23}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 33}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 24}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 16}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 25}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 33}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 26}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 16}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 25}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 33}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 26}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 16}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 26}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 33}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 27}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 16}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 7}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 6}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 83}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 4}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 19}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 18}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 58}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 74}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 5}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 31}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 1}}}, "s": {"0": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1474}, "end": {"line": 63, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1474}, "end": {"line": 63, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1474}, "end": {"line": 63, "column": 1}}, "loc": {"start": {"line": 1, "column": 1474}, "end": {"line": 63, "column": 1}}, "line": 1}}, "f": {"0": 0}}}