{"name": "choreo-reading-list-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "check-all": "npm run type-check && npm run lint && npm run format:check", "fix-all": "npm run type-check && npm run lint:fix && npm run format"}, "dependencies": {"@headlessui/react": "^1.7.4", "@heroicons/react": "^2.0.13", "@radix-ui/react-slot": "^1.2.3", "axios": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.460.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-toastify": "^9.1.3", "tailwind-merge": "^2.6.0", "uuid": "^9.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.191", "@types/node": "^24.0.12", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/uuid": "^9.0.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vite": "^5.4.19", "vitest": "^3.2.4"}}